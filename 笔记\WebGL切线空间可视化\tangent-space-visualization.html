<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>WebGL 切线空间计算可视化</title>
        <style>
            body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                background: #1a1a1a;
                color: #fff;
            }
            .container {
                display: flex;
                gap: 20px;
            }
            .canvas-container {
                flex: 1;
            }
            canvas {
                border: 1px solid #444;
                background: #000;
            }
            .controls {
                width: 300px;
                background: #2a2a2a;
                padding: 20px;
                border-radius: 8px;
            }
            .control-group {
                margin-bottom: 20px;
            }
            label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            input[type='range'] {
                width: 100%;
                margin-bottom: 10px;
            }
            .explanation {
                background: #333;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                font-size: 14px;
                line-height: 1.5;
            }
            .vector-info {
                background: #444;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 10px;
                font-size: 12px;
            }
            .highlight {
                color: #4caf50;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <h1>WebGL 切线空间计算可视化</h1>

        <div class="explanation">
            <h3>偏导数与切线空间</h3>
            <p><strong>dFdx/dFdy</strong> 是 GLSL 中的偏导数函数，用于计算相邻像素间的变化率：</p>
            <ul>
                <li><span class="highlight">pos_dx = dFdx(vMPos.xyz)</span> - 世界位置在屏幕X方向的变化率</li>
                <li><span class="highlight">pos_dy = dFdy(vMPos.xyz)</span> - 世界位置在屏幕Y方向的变化率</li>
                <li><span class="highlight">tex_dx = dFdx(vUv)</span> - UV坐标在屏幕X方向的变化率</li>
                <li><span class="highlight">tex_dy = dFdy(vUv)</span> - UV坐标在屏幕Y方向的变化率</li>
            </ul>
            <p>通过这些偏导数，我们可以重建切线空间的基向量 T（切线）、B（副切线）、N（法线）。</p>
        </div>

        <div class="container">
            <div class="canvas-container">
                <canvas id="canvas" width="600" height="600"></canvas>
            </div>

            <div class="controls">
                <div class="control-group">
                    <label>旋转角度</label>
                    <input type="range" id="rotation" min="0" max="360" value="45" />
                    <span id="rotationValue">45°</span>
                </div>

                <div class="control-group">
                    <label>UV 扭曲强度</label>
                    <input type="range" id="uvDistortion" min="0" max="100" value="20" />
                    <span id="uvDistortionValue">20%</span>
                </div>

                <div class="control-group">
                    <label>显示选项</label>
                    <label><input type="checkbox" id="showTangent" checked /> 显示切线向量 (T)</label>
                    <label><input type="checkbox" id="showBitangent" checked /> 显示副切线向量 (B)</label>
                    <label><input type="checkbox" id="showNormal" checked /> 显示法线向量 (N)</label>
                    <label><input type="checkbox" id="showGrid" checked /> 显示UV网格</label>
                </div>

                <div class="vector-info">
                    <h4>向量计算公式</h4>
                    <p>
                        <strong>切线 T:</strong><br />
                        normalize(pos_dx * tex_dy.t - pos_dy * tex_dx.t)
                    </p>
                    <p>
                        <strong>副切线 B:</strong><br />
                        normalize(-pos_dx * tex_dy.s + pos_dy * tex_dx.s)
                    </p>
                    <p>
                        <strong>TBN矩阵:</strong><br />
                        mat3(T, B, normalize(N))
                    </p>
                </div>

                <div class="vector-info">
                    <h4>当前向量值</h4>
                    <p id="tangentVector">T: (0, 0, 0)</p>
                    <p id="bitangentVector">B: (0, 0, 0)</p>
                    <p id="normalVector">N: (0, 0, 1)</p>
                </div>
            </div>
        </div>

        <script>
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');

            // 控制元素
            const rotationSlider = document.getElementById('rotation');
            const uvDistortionSlider = document.getElementById('uvDistortion');
            const showTangent = document.getElementById('showTangent');
            const showBitangent = document.getElementById('showBitangent');
            const showNormal = document.getElementById('showNormal');
            const showGrid = document.getElementById('showGrid');

            // 向量显示元素
            const tangentVector = document.getElementById('tangentVector');
            const bitangentVector = document.getElementById('bitangentVector');
            const normalVector = document.getElementById('normalVector');

            let rotation = 45;
            let uvDistortion = 20;

            // 3D 向量类
            class Vector3 {
                constructor(x = 0, y = 0, z = 0) {
                    this.x = x;
                    this.y = y;
                    this.z = z;
                }

                normalize() {
                    const len = Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
                    if (len > 0) {
                        this.x /= len;
                        this.y /= len;
                        this.z /= len;
                    }
                    return this;
                }

                subtract(v) {
                    return new Vector3(this.x - v.x, this.y - v.y, this.z - v.z);
                }

                cross(v) {
                    return new Vector3(this.y * v.z - this.z * v.y, this.z * v.x - this.x * v.z, this.x * v.y - this.y * v.x);
                }
            }

            // 2D 向量类
            class Vector2 {
                constructor(x = 0, y = 0) {
                    this.x = x;
                    this.y = y;
                }
            }

            // 模拟偏导数计算
            function calculateDerivatives(u, v, distortion) {
                const epsilon = 0.1; // 增大epsilon让变化更明显

                // 模拟世界位置（带有一些变形）
                const getWorldPos = (u, v) => {
                    const distortionFactor = distortion / 100;
                    return new Vector3(
                        u * 2 - 1 + Math.sin(v * Math.PI * 2) * distortionFactor,
                        v * 2 - 1 + Math.sin(u * Math.PI * 2) * distortionFactor,
                        Math.sin(u * Math.PI) * Math.sin(v * Math.PI) * distortionFactor * 2
                    );
                };

                // 计算位置偏导数
                const pos_center = getWorldPos(u, v);
                const pos_dx = getWorldPos(u + epsilon, v).subtract(pos_center);
                const pos_dy = getWorldPos(u, v + epsilon).subtract(pos_center);

                // UV 偏导数 - 模拟屏幕空间的UV变化
                const tex_dx = new Vector2(epsilon, 0);
                const tex_dy = new Vector2(0, epsilon);

                return { pos_dx, pos_dy, tex_dx, tex_dy };
            }

            // 计算切线空间基向量
            function calculateTangentSpace(u, v, distortion) {
                const { pos_dx, pos_dy, tex_dx, tex_dy } = calculateDerivatives(u, v, distortion);

                // 计算切线向量 T
                let t = new Vector3(pos_dx.x * tex_dy.y - pos_dy.x * tex_dx.y, pos_dx.y * tex_dy.y - pos_dy.y * tex_dx.y, pos_dx.z * tex_dy.y - pos_dy.z * tex_dx.y);

                // 如果切线向量太小，使用默认值
                if (t.x * t.x + t.y * t.y + t.z * t.z < 0.0001) {
                    t = new Vector3(1, 0, 0);
                } else {
                    t.normalize();
                }

                // 计算副切线向量 B
                let b = new Vector3(-pos_dx.x * tex_dy.x + pos_dy.x * tex_dx.x, -pos_dx.y * tex_dy.x + pos_dy.y * tex_dx.x, -pos_dx.z * tex_dy.x + pos_dy.z * tex_dx.x);

                // 如果副切线向量太小，使用默认值
                if (b.x * b.x + b.y * b.y + b.z * b.z < 0.0001) {
                    b = new Vector3(0, 1, 0);
                } else {
                    b.normalize();
                }

                // 法线向量 - 使用叉积计算
                let n = t.cross(b);

                // 如果叉积结果为零向量，使用默认法线
                if (n.x * n.x + n.y * n.y + n.z * n.z < 0.0001) {
                    n = new Vector3(0, 0, 1);
                } else {
                    n.normalize();
                    // 确保法线指向正确方向（向上）
                    if (n.z < 0) {
                        n.x = -n.x;
                        n.y = -n.y;
                        n.z = -n.z;
                    }
                }

                return { t, b, n };
            }

            // 3D 到 2D 投影
            function project3D(point, rotation) {
                const rad = (rotation * Math.PI) / 180;
                const cos = Math.cos(rad);
                const sin = Math.sin(rad);

                // 简单的等距投影
                const x = point.x * cos - point.z * sin;
                const y = point.y;

                return {
                    x: canvas.width / 2 + x * 200,
                    y: canvas.height / 2 - y * 200,
                };
            }

            // 绘制向量
            function drawVector(start, direction, color, label, scale = 100) {
                const end = {
                    x: start.x + direction.x * scale,
                    y: start.y - direction.y * scale, // Y轴翻转
                };

                ctx.strokeStyle = color;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(start.x, start.y);
                ctx.lineTo(end.x, end.y);
                ctx.stroke();

                // 绘制箭头
                const angle = Math.atan2(end.y - start.y, end.x - start.x);
                const arrowLength = 15;
                ctx.beginPath();
                ctx.moveTo(end.x, end.y);
                ctx.lineTo(end.x - arrowLength * Math.cos(angle - Math.PI / 6), end.y - arrowLength * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(end.x, end.y);
                ctx.lineTo(end.x - arrowLength * Math.cos(angle + Math.PI / 6), end.y - arrowLength * Math.sin(angle + Math.PI / 6));
                ctx.stroke();

                // 标签
                ctx.fillStyle = color;
                ctx.font = '16px Arial';
                ctx.fillText(label, end.x + 10, end.y - 10);
            }

            // 主渲染函数
            function render() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                const centerU = 0.5;
                const centerV = 0.5;
                const { t, b, n } = calculateTangentSpace(centerU, centerV, uvDistortion);

                // 更新向量显示
                tangentVector.textContent = `T: (${t.x.toFixed(3)}, ${t.y.toFixed(3)}, ${t.z.toFixed(3)})`;
                bitangentVector.textContent = `B: (${b.x.toFixed(3)}, ${b.y.toFixed(3)}, ${b.z.toFixed(3)})`;
                normalVector.textContent = `N: (${n.x.toFixed(3)}, ${n.y.toFixed(3)}, ${n.z.toFixed(3)})`;

                const center = { x: canvas.width / 2, y: canvas.height / 2 };

                // 绘制UV网格
                if (showGrid.checked) {
                    ctx.strokeStyle = '#444';
                    ctx.lineWidth = 1;
                    for (let i = 0; i <= 10; i++) {
                        const u = i / 10;
                        const v = i / 10;

                        // 水平线
                        ctx.beginPath();
                        for (let j = 0; j <= 10; j++) {
                            const pos = project3D(calculateDerivatives(j / 10, v, uvDistortion).pos_dx, rotation);
                            if (j === 0) ctx.moveTo(pos.x, pos.y);
                            else ctx.lineTo(pos.x, pos.y);
                        }
                        ctx.stroke();

                        // 垂直线
                        ctx.beginPath();
                        for (let j = 0; j <= 10; j++) {
                            const pos = project3D(calculateDerivatives(u, j / 10, uvDistortion).pos_dx, rotation);
                            if (j === 0) ctx.moveTo(pos.x, pos.y);
                            else ctx.lineTo(pos.x, pos.y);
                        }
                        ctx.stroke();
                    }
                }

                // 绘制向量
                if (showTangent.checked) {
                    drawVector(center, t, '#ff4444', 'T (切线)', 120);
                }
                if (showBitangent.checked) {
                    drawVector(center, b, '#44ff44', 'B (副切线)', 120);
                }
                if (showNormal.checked) {
                    drawVector(center, n, '#4444ff', 'N (法线)', 120);
                }

                // 绘制坐标系原点
                ctx.fillStyle = '#fff';
                ctx.beginPath();
                ctx.arc(center.x, center.y, 5, 0, 2 * Math.PI);
                ctx.fill();
            }

            // 事件监听器
            rotationSlider.addEventListener('input', (e) => {
                rotation = parseInt(e.target.value);
                document.getElementById('rotationValue').textContent = rotation + '°';
                render();
            });

            uvDistortionSlider.addEventListener('input', (e) => {
                uvDistortion = parseInt(e.target.value);
                document.getElementById('uvDistortionValue').textContent = uvDistortion + '%';
                render();
            });

            [showTangent, showBitangent, showNormal, showGrid].forEach((checkbox) => {
                checkbox.addEventListener('change', render);
            });

            // 初始渲染
            render();
        </script>
    </body>
</html>
