<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>切线空间法线向量演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #fff;
        }
        canvas {
            border: 1px solid #444;
            background: #000;
            display: block;
            margin: 20px auto;
        }
        .explanation {
            max-width: 800px;
            margin: 0 auto;
            background: #333;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .formula {
            background: #444;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .highlight {
            color: #4CAF50;
            font-weight: bold;
        }
        .vector-colors {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
        }
        .vector-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .color-box {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center;">切线空间法线向量计算演示</h1>
    
    <div class="explanation">
        <h3>核心概念：通过偏导数重建切线空间</h3>
        <p>在片段着色器中，我们可以使用 <span class="highlight">dFdx()</span> 和 <span class="highlight">dFdy()</span> 函数获取相邻像素间的变化率，然后重建切线空间的三个基向量：</p>
        
        <div class="formula">
// 获取偏导数
vec3 pos_dx = dFdx(vMPos.xyz);  // 世界位置在X方向的变化率
vec3 pos_dy = dFdy(vMPos.xyz);  // 世界位置在Y方向的变化率
vec2 tex_dx = dFdx(vUv);        // UV坐标在X方向的变化率
vec2 tex_dy = dFdy(vUv);        // UV坐标在Y方向的变化率

// 计算切线空间基向量
vec3 T = normalize(pos_dx * tex_dy.t - pos_dy * tex_dx.t);  // 切线
vec3 B = normalize(-pos_dx * tex_dy.s + pos_dy * tex_dx.s); // 副切线
vec3 N = normalize(cross(T, B));                            // 法线
        </div>
        
        <p><strong>关键理解：</strong>法线向量 N 是通过切线向量 T 和副切线向量 B 的<span class="highlight">叉积</span>计算得出的，确保三个向量相互正交。</p>
    </div>

    <div class="vector-colors">
        <div class="vector-item">
            <div class="color-box" style="background: #ff4444;"></div>
            <span>T (切线向量)</span>
        </div>
        <div class="vector-item">
            <div class="color-box" style="background: #44ff44;"></div>
            <span>B (副切线向量)</span>
        </div>
        <div class="vector-item">
            <div class="color-box" style="background: #4444ff;"></div>
            <span>N (法线向量)</span>
        </div>
    </div>

    <canvas id="canvas" width="600" height="400"></canvas>

    <div class="explanation">
        <h3>数学原理</h3>
        <p>这个方法基于以下数学关系：</p>
        <div class="formula">
∂P/∂screen_x = T * ∂u/∂screen_x + B * ∂v/∂screen_x
∂P/∂screen_y = T * ∂u/∂screen_y + B * ∂v/∂screen_y
        </div>
        <p>通过求解这个线性方程组，我们可以得到切线 T 和副切线 B，然后通过叉积计算法线 N。</p>
        
        <p><strong>优势：</strong></p>
        <ul>
            <li>不需要预计算切线属性</li>
            <li>适用于任意几何体</li>
            <li>可以处理动态变形</li>
        </ul>
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 简单的3D向量类
        class Vector3 {
            constructor(x = 0, y = 0, z = 0) {
                this.x = x;
                this.y = y;
                this.z = z;
            }
            
            normalize() {
                const len = Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
                if (len > 0) {
                    this.x /= len;
                    this.y /= len;
                    this.z /= len;
                }
                return this;
            }
            
            cross(v) {
                return new Vector3(
                    this.y * v.z - this.z * v.y,
                    this.z * v.x - this.x * v.z,
                    this.x * v.y - this.y * v.x
                );
            }
        }
        
        // 绘制向量
        function drawVector(start, direction, color, label, scale = 100) {
            const end = {
                x: start.x + direction.x * scale,
                y: start.y - direction.y * scale  // Y轴翻转
            };
            
            ctx.strokeStyle = color;
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(start.x, start.y);
            ctx.lineTo(end.x, end.y);
            ctx.stroke();
            
            // 绘制箭头
            const angle = Math.atan2(end.y - start.y, end.x - start.x);
            const arrowLength = 20;
            ctx.beginPath();
            ctx.moveTo(end.x, end.y);
            ctx.lineTo(
                end.x - arrowLength * Math.cos(angle - Math.PI / 6),
                end.y - arrowLength * Math.sin(angle - Math.PI / 6)
            );
            ctx.moveTo(end.x, end.y);
            ctx.lineTo(
                end.x - arrowLength * Math.cos(angle + Math.PI / 6),
                end.y - arrowLength * Math.sin(angle + Math.PI / 6)
            );
            ctx.stroke();
            
            // 标签
            ctx.fillStyle = color;
            ctx.font = 'bold 16px Arial';
            ctx.fillText(label, end.x + 15, end.y - 10);
        }
        
        // 绘制坐标系
        function drawCoordinateSystem() {
            const center = { x: canvas.width / 2, y: canvas.height / 2 };
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景网格
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }
            
            // 定义三个正交向量（简化示例）
            const T = new Vector3(1, 0, 0);      // 切线向量（红色）
            const B = new Vector3(0, 1, 0);      // 副切线向量（绿色）
            const N = T.cross(B).normalize();    // 法线向量（蓝色）
            
            // 绘制向量
            drawVector(center, T, '#ff4444', 'T (切线)', 120);
            drawVector(center, B, '#44ff44', 'B (副切线)', 120);
            drawVector(center, N, '#4444ff', 'N (法线)', 120);
            
            // 绘制原点
            ctx.fillStyle = '#fff';
            ctx.beginPath();
            ctx.arc(center.x, center.y, 8, 0, 2 * Math.PI);
            ctx.fill();
            
            // 添加说明文字
            ctx.fillStyle = '#fff';
            ctx.font = '14px Arial';
            ctx.fillText('N = T × B (叉积)', center.x + 20, center.y - 140);
            ctx.fillText('三个向量相互正交', center.x + 20, center.y - 120);
        }
        
        // 动画效果
        let angle = 0;
        function animate() {
            const center = { x: canvas.width / 2, y: canvas.height / 2 };
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景网格
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }
            
            // 旋转的切线和副切线向量
            const cos = Math.cos(angle);
            const sin = Math.sin(angle);
            
            const T = new Vector3(cos, sin, 0).normalize();
            const B = new Vector3(-sin, cos, 0).normalize();
            const N = T.cross(B).normalize();
            
            // 绘制向量
            drawVector(center, T, '#ff4444', 'T', 120);
            drawVector(center, B, '#44ff44', 'B', 120);
            drawVector(center, N, '#4444ff', 'N', 120);
            
            // 绘制原点
            ctx.fillStyle = '#fff';
            ctx.beginPath();
            ctx.arc(center.x, center.y, 8, 0, 2 * Math.PI);
            ctx.fill();
            
            // 显示向量值
            ctx.fillStyle = '#fff';
            ctx.font = '12px Arial';
            ctx.fillText(`T: (${T.x.toFixed(2)}, ${T.y.toFixed(2)}, ${T.z.toFixed(2)})`, 10, 30);
            ctx.fillText(`B: (${B.x.toFixed(2)}, ${B.y.toFixed(2)}, ${B.z.toFixed(2)})`, 10, 50);
            ctx.fillText(`N: (${N.x.toFixed(2)}, ${N.y.toFixed(2)}, ${N.z.toFixed(2)})`, 10, 70);
            
            angle += 0.02;
            requestAnimationFrame(animate);
        }
        
        // 开始动画
        animate();
    </script>
</body>
</html>
