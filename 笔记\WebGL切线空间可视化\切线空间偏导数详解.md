# WebGL 切线空间偏导数计算详解

## 核心概念

### 1. 偏导数函数 (dFdx/dFdy)

在 GLSL 中，`dFdx()` 和 `dFdy()` 是**偏导数函数**，用于计算相邻像素间的变化率：

```glsl
vec3 pos_dx = dFdx(vMPos.xyz);  // 世界位置在屏幕X方向的变化率
vec3 pos_dy = dFdy(vMPos.xyz);  // 世界位置在屏幕Y方向的变化率
vec2 tex_dx = dFdx(vUv);        // UV坐标在屏幕X方向的变化率
vec2 tex_dy = dFdy(vUv);        // UV坐标在屏幕Y方向的变化率
```

#### 工作原理
- GPU 以 2×2 像素块（quad）为单位处理片段着色器
- `dFdx(value)` 返回当前像素与右侧像素的差值
- `dFdy(value)` 返回当前像素与下方像素的差值
- 这些差值表示该变量在屏幕空间中的变化率

### 2. 切线空间基向量计算

#### 数学原理
切线空间是一个局部坐标系，由三个正交向量组成：
- **T (Tangent)**: 切线向量，沿着UV坐标的U方向
- **B (Bitangent)**: 副切线向量，沿着UV坐标的V方向  
- **N (Normal)**: 法线向量，垂直于表面

#### 计算公式

```glsl
// 切线向量 T
vec3 t = normalize(pos_dx * tex_dy.t - pos_dy * tex_dx.t);

// 副切线向量 B  
vec3 b = normalize(-pos_dx * tex_dy.s + pos_dy * tex_dx.s);

// TBN 变换矩阵
mat3 tbn = mat3(t, b, normalize(vNormal));
```

#### 公式推导

这个计算基于以下数学关系：

```
∂P/∂u = T * ∂u/∂x + B * ∂v/∂x
∂P/∂v = T * ∂u/∂y + B * ∂v/∂y
```

其中：
- `P` 是世界空间位置
- `u, v` 是纹理坐标
- `x, y` 是屏幕空间坐标

通过求解这个线性方程组，我们得到：

```
T = (∂P/∂x * ∂v/∂y - ∂P/∂y * ∂v/∂x) / (∂u/∂x * ∂v/∂y - ∂u/∂y * ∂v/∂x)
B = (∂P/∂y * ∂u/∂x - ∂P/∂x * ∂u/∂y) / (∂u/∂x * ∂v/∂y - ∂u/∂y * ∂v/∂x)
```

### 3. 实际应用场景

#### 法线贴图
切线空间最常用于法线贴图：

```glsl
// 从法线贴图采样
vec3 normalMap = texture2D(u_normalTexture, vUv).xyz * 2.0 - 1.0;

// 构建TBN矩阵
mat3 tbn = mat3(t, b, normalize(vNormal));

// 将切线空间法线转换到世界空间
vec3 worldNormal = tbn * normalMap;
```

#### 视差贴图
用于计算视差偏移：

```glsl
// 计算视线方向在切线空间中的表示
vec3 viewDir = normalize(tbn * (u_cameraPosition - vMPos.xyz));

// 使用视线方向计算视差偏移
vec2 parallaxOffset = viewDir.xy * heightScale * texture2D(u_heightTexture, vUv).r;
```

### 4. 优势与限制

#### 优势
1. **无需预计算**: 不需要在顶点数据中存储切线属性
2. **适用性广**: 适用于任意几何体，包括程序生成的网格
3. **内存节省**: 减少顶点缓冲区大小
4. **动态计算**: 可以处理动态变形的几何体

#### 限制
1. **性能开销**: 每个像素都需要计算偏导数
2. **精度问题**: 在几何体边缘或UV接缝处可能不稳定
3. **GPU要求**: 需要支持偏导数指令的GPU

### 5. 调试技巧

#### 可视化切线空间
```glsl
// 将切线空间向量可视化为颜色
gl_FragColor = vec4(t * 0.5 + 0.5, 1.0);  // 切线向量
gl_FragColor = vec4(b * 0.5 + 0.5, 1.0);  // 副切线向量
gl_FragColor = vec4(normalize(vNormal) * 0.5 + 0.5, 1.0);  // 法线向量
```

#### 检查正交性
```glsl
// 检查向量是否正交（点积应该接近0）
float orthogonality = abs(dot(t, b));
if (orthogonality > 0.1) {
    gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0);  // 红色表示错误
}
```

### 6. 性能优化建议

1. **条件编译**: 只在需要时启用切线空间计算
2. **LOD系统**: 在远距离时使用简化的法线计算
3. **预计算**: 对于静态几何体，考虑预计算切线属性
4. **精度平衡**: 根据需求选择合适的精度级别

## 总结

偏导数方法提供了一种灵活的切线空间计算方式，特别适合现代GPU渲染管线。虽然有一定的性能开销，但其灵活性和通用性使其成为许多高级渲染技术的基础。

通过理解这些概念，您可以更好地实现法线贴图、视差贴图、各向异性反射等高级渲染效果。
